-- Initial schema for MCP Server
-- This migration creates the basic tables needed for MCP functionality

-- Table for storing MCP tools/functions
CREATE TABLE mcp_tools (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    input_schema JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table for storing MCP resources
CREATE TABLE mcp_resources (
    id BIGSERIAL PRIMARY KEY,
    uri VARCHAR(500) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    mime_type VARCHAR(100),
    content TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table for storing MCP prompts
CREATE TABLE mcp_prompts (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    template TEXT NOT NULL,
    arguments JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table for storing conversation sessions
CREATE TABLE mcp_sessions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    client_info JSONB,
    server_info JSONB,
    capabilities JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'ACTIVE'
);

-- Table for storing tool execution logs
CREATE TABLE mcp_tool_executions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) REFERENCES mcp_sessions(session_id),
    tool_name VARCHAR(255) NOT NULL,
    input_data JSONB,
    output_data JSONB,
    execution_time_ms BIGINT,
    status VARCHAR(50) NOT NULL,
    error_message TEXT,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX idx_mcp_tools_name ON mcp_tools(name);
CREATE INDEX idx_mcp_resources_uri ON mcp_resources(uri);
CREATE INDEX idx_mcp_resources_mime_type ON mcp_resources(mime_type);
CREATE INDEX idx_mcp_prompts_name ON mcp_prompts(name);
CREATE INDEX idx_mcp_sessions_session_id ON mcp_sessions(session_id);
CREATE INDEX idx_mcp_sessions_status ON mcp_sessions(status);
CREATE INDEX idx_mcp_tool_executions_session_id ON mcp_tool_executions(session_id);
CREATE INDEX idx_mcp_tool_executions_tool_name ON mcp_tool_executions(tool_name);
CREATE INDEX idx_mcp_tool_executions_executed_at ON mcp_tool_executions(executed_at);

-- Insert some sample data
INSERT INTO mcp_tools (name, description, input_schema) VALUES
('echo', 'Simple echo tool that returns the input', '{"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}'),
('current_time', 'Returns the current server time', '{"type": "object", "properties": {}}'),
('calculate', 'Performs basic mathematical calculations', '{"type": "object", "properties": {"expression": {"type": "string"}}, "required": ["expression"]}'),
('lead-report', 'Generate lead reports with grouping, filtering, and various metrics (COUNT, SUM, AVERAGE). Supports complex queries with date ranges, rules, and multiple grouping fields.',
 '{"type": "object", "properties": {"reportRequest": {"type": "object", "description": "Complete lead report request object", "properties": {"rules": {"type": "array", "description": "Filtering rules to apply", "items": {"type": "object", "properties": {"field": {"type": "string"}, "operator": {"type": "string"}, "value": {"type": "string"}, "condition": {"type": "string"}}}}, "groupBy": {"type": "array", "description": "Fields to group by", "items": {"type": "object", "properties": {"name": {"type": "string"}, "format": {"type": "string"}, "primaryField": {"type": "string"}, "property": {"type": "string"}}}}, "dateRange": {"type": "object", "description": "Date range filter", "properties": {"operator": {"type": "string", "enum": ["current_year", "current_month", "last_30_days"]}, "field": {"type": "string"}, "id": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}, "from": {"type": "string"}, "to": {"type": "string"}, "fieldInputType": {"type": "string"}, "primaryField": {"type": "string"}, "property": {"type": "string"}}}, "metrics": {"type": "array", "description": "Metrics to calculate", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["COUNT", "SUM", "AVERAGE"]}, "field": {"type": "string"}, "header": {"type": "string"}}}}, "category": {"type": "string"}}}, "payload": {"type": "string", "description": "JSON string containing the complete report request"}}, "oneOf": [{"required": ["reportRequest"]}, {"required": ["payload"]}]}');

INSERT INTO mcp_prompts (name, description, template, arguments) VALUES
('greeting', 'A friendly greeting prompt', 'Hello {{name}}! Welcome to the MCP server. How can I assist you today?', '{"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"]}'),
('help', 'Provides help information', 'Available commands and tools:\n{{tools}}\n\nFor more information about a specific tool, use the describe command.', '{"type": "object", "properties": {"tools": {"type": "array"}}}');
