/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/dto/leadreport/LeadReportResponse.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/entity/McpTool.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/McpServerApplication.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/tool/LeadReportTool.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/service/PromptProcessingService.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/service/McpToolService.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/entity/McpSession.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/config/BearerTokenAuthenticationFilter.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/service/LeadReportService.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/config/SecurityConfig.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/dto/leadreport/ReportRule.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/repository/McpToolRepository.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/dto/leadreport/DateRange.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/dto/leadreport/Metric.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/controller/McpController.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/repository/McpSessionRepository.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/tool/LeadReportPromptTool.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/config/McpServerConfig.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/dto/leadreport/LeadReportRequest.java
/home/<USER>/repo/sd-mcp/src/main/java/com/sell/mcp/dto/leadreport/GroupByField.java
