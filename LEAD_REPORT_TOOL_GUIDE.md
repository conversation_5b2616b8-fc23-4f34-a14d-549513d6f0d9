# Lead Report Tool Guide

This guide provides comprehensive documentation for using the Lead Report tools in the MCP Server.

## Available Tools

### 1. `lead-report` - Structured Report Tool
For advanced users who want full control over report parameters.

### 2. `lead-report-prompt` - Natural Language Tool
For easy report generation using natural language prompts.

## Tool: `lead-report-prompt` (Recommended)

### Description
Generate lead reports using natural language prompts. Perfect for MCP clients that want to provide a conversational interface.

### Usage Examples

#### Basic Count Reports
```json
{
  "tool": "lead-report-prompt",
  "input": {
    "prompt": "count of leads by owner"
  }
}
```

```json
{
  "tool": "lead-report-prompt", 
  "input": {
    "prompt": "show leads by source"
  }
}
```

#### Reports with Date Filters
```json
{
  "tool": "lead-report-prompt",
  "input": {
    "prompt": "count of leads by owner this year"
  }
}
```

```json
{
  "tool": "lead-report-prompt",
  "input": {
    "prompt": "show leads by status this month"
  }
}
```

```json
{
  "tool": "lead-report-prompt",
  "input": {
    "prompt": "count of leads by source last 30 days"
  }
}
```

#### Sum and Average Reports
```json
{
  "tool": "lead-report-prompt",
  "input": {
    "prompt": "sum of value by owner"
  }
}
```

```json
{
  "tool": "lead-report-prompt",
  "input": {
    "prompt": "average score by source"
  }
}
```

```json
{
  "tool": "lead-report-prompt",
  "input": {
    "prompt": "total value by owner this month"
  }
}
```

### Supported Prompt Patterns

1. **Count Pattern**: `count of {entity} by {field}`
2. **Sum Pattern**: `sum of {field} by {groupBy}` or `total {field} by {groupBy}`
3. **Average Pattern**: `average {field} by {groupBy}` or `avg {field} by {groupBy}`
4. **Show Pattern**: `show {entity} by {field}` (defaults to count)

### Supported Fields

- **Entities**: `lead`, `leads`
- **Group By Fields**: `owner`, `ownerId`, `source`, `status`
- **Metric Fields**: `value`, `score`, `id` (for counting)
- **Date Ranges**: `this year`, `current year`, `this month`, `current month`, `last 30 days`

## Tool: `lead-report` - Advanced Structured Tool

### Description
Generate lead reports using the complete structured payload format. Provides maximum flexibility and control.

### Usage Example

```json
{
  "tool": "lead-report",
  "input": {
    "reportRequest": {
      "rules": [],
      "groupBy": [
        {
          "name": "ownerId",
          "format": null,
          "primaryField": null,
          "property": null
        }
      ],
      "dateRange": {
        "operator": "current_year",
        "field": "createdAt",
        "id": "createdAt",
        "type": "date",
        "value": null,
        "from": "00:00",
        "to": "23:59",
        "fieldInputType": "DATETIME_PICKER",
        "primaryField": null,
        "property": null
      },
      "metrics": [
        {
          "type": "COUNT",
          "field": "id",
          "header": null
        }
      ],
      "colorCodes": null,
      "category": "ONE_DIMENSIONAL"
    }
  }
}
```

### Alternative Payload Format

```json
{
  "tool": "lead-report",
  "input": {
    "payload": "{\"rules\":[],\"groupBy\":[{\"name\":\"ownerId\",\"format\":null,\"primaryField\":null,\"property\":null}],\"dateRange\":{\"operator\":\"current_year\",\"field\":\"createdAt\",\"id\":\"createdAt\",\"type\":\"date\",\"value\":null,\"from\":\"00:00\",\"to\":\"23:59\",\"fieldInputType\":\"DATETIME_PICKER\",\"primaryField\":null,\"property\":null},\"metrics\":[{\"type\":\"COUNT\",\"field\":\"id\",\"header\":null}],\"colorCodes\":null,\"category\":\"ONE_DIMENSIONAL\"}"
  }
}
```

## Response Format

Both tools return the same response format:

```json
{
  "success": true,
  "tool": "lead-report-prompt",
  "prompt": "count of leads by owner",
  "result": {
    "reportId": "uuid-here",
    "title": "COUNT of id by ownerId",
    "generatedAt": "2024-01-15T10:30:00",
    "data": [
      {
        "groupValues": {"ownerId": "john.doe"},
        "metricValues": {"id": 25}
      },
      {
        "groupValues": {"ownerId": "jane.smith"},
        "metricValues": {"id": 18}
      }
    ],
    "summary": {
      "totalRows": 2,
      "aggregates": {"total_id": 43}
    },
    "metadata": {
      "totalRecordsProcessed": 100,
      "generatedAt": "2024-01-15T10:30:00",
      "category": "ONE_DIMENSIONAL"
    }
  }
}
```

## MCP Client Integration Examples

### Python MCP Client

```python
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def generate_lead_report():
    server_params = StdioServerParameters(
        command="java",
        args=["-jar", "mcp-server.jar"]
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize the session
            await session.initialize()
            
            # Generate report using natural language
            result = await session.call_tool(
                "lead-report-prompt",
                {"prompt": "count of leads by owner this month"}
            )
            
            print(f"Report generated: {result}")

# Run the example
asyncio.run(generate_lead_report())
```

### JavaScript MCP Client

```javascript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function generateLeadReport() {
  const transport = new StdioClientTransport({
    command: 'java',
    args: ['-jar', 'mcp-server.jar']
  });

  const client = new Client({
    name: "lead-report-client",
    version: "1.0.0"
  }, {
    capabilities: {}
  });

  await client.connect(transport);

  // Generate report
  const result = await client.callTool({
    name: "lead-report-prompt",
    arguments: {
      prompt: "sum of value by owner this year"
    }
  });

  console.log('Report result:', result);
}

generateLeadReport().catch(console.error);
```

## Error Handling

### Common Errors

1. **Invalid Prompt Format**
```json
{
  "success": false,
  "tool": "lead-report-prompt",
  "error": "Could not parse prompt format",
  "examples": ["count of leads by owner", "show leads by source"]
}
```

2. **Missing Required Fields**
```json
{
  "success": false,
  "tool": "lead-report",
  "error": "Missing required field: reportRequest or payload"
}
```

## Best Practices

1. **Use Natural Language Tool First**: Start with `lead-report-prompt` for most use cases
2. **Fallback to Structured Tool**: Use `lead-report` when you need complex filtering rules
3. **Handle Errors Gracefully**: Always check the `success` field in responses
4. **Cache Results**: Reports can be expensive to generate, consider caching results
5. **Validate Prompts**: Test prompts with the examples provided

## Supported Metrics

- **COUNT**: Count of records in each group
- **SUM**: Sum of numeric field values in each group  
- **AVERAGE/AVG**: Average of numeric field values in each group

## Supported Date Ranges

- **current_year**: From January 1st to December 31st of current year
- **current_month**: From 1st to last day of current month
- **last_30_days**: From 30 days ago to today

## Field Mappings

| Natural Language | Database Field |
|------------------|----------------|
| lead, leads      | id             |
| owner, owners    | ownerId        |
| source, sources  | source         |
| status           | status         |
| value, values    | value          |
| score, scores    | score          |
