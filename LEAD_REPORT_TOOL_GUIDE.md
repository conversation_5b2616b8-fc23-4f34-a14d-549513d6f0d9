# Lead Report Tool Guide

This guide provides comprehensive documentation for using the Lead Report tool in the MCP Server.

## Available Tool

### `lead-report` - Structured Report Tool
Generate lead reports using structured JSON payload. Provides maximum flexibility and control over report parameters.

**Note**: MCP clients are responsible for converting natural language prompts to the structured JSON format. This server only processes the structured payload.

## Usage Examples

### Basic Count Report - Count of Leads by Owner

```json
{
  "tool": "lead-report",
  "input": {
    "reportRequest": {
      "rules": [],
      "groupBy": [
        {
          "name": "ownerId",
          "format": null,
          "primaryField": null,
          "property": null
        }
      ],
      "dateRange": {
        "operator": "current_year",
        "field": "createdAt",
        "id": "createdAt",
        "type": "date",
        "value": null,
        "from": "00:00",
        "to": "23:59",
        "fieldInputType": "DATETIME_PICKER",
        "primaryField": null,
        "property": null
      },
      "metrics": [
        {
          "type": "COUNT",
          "field": "id",
          "header": null
        }
      ],
      "colorCodes": null,
      "category": "ONE_DIMENSIONAL"
    }
  }
}
```

### Sum Report - Total Value by Owner This Year

```json
{
  "tool": "lead-report",
  "input": {
    "reportRequest": {
      "rules": [],
      "groupBy": [
        {
          "name": "ownerId",
          "format": null,
          "primaryField": null,
          "property": null
        }
      ],
      "dateRange": {
        "operator": "current_year",
        "field": "createdAt",
        "id": "createdAt",
        "type": "date",
        "value": null,
        "from": "00:00",
        "to": "23:59",
        "fieldInputType": "DATETIME_PICKER",
        "primaryField": null,
        "property": null
      },
      "metrics": [
        {
          "type": "SUM",
          "field": "value",
          "header": null
        }
      ],
      "colorCodes": null,
      "category": "ONE_DIMENSIONAL"
    }
  }
}
```

### Average Report - Average Score by Source

```json
{
  "tool": "lead-report",
  "input": {
    "reportRequest": {
      "rules": [],
      "groupBy": [
        {
          "name": "source",
          "format": null,
          "primaryField": null,
          "property": null
        }
      ],
      "dateRange": {
        "operator": "current_month",
        "field": "createdAt",
        "id": "createdAt",
        "type": "date",
        "value": null,
        "from": "00:00",
        "to": "23:59",
        "fieldInputType": "DATETIME_PICKER",
        "primaryField": null,
        "property": null
      },
      "metrics": [
        {
          "type": "AVERAGE",
          "field": "score",
          "header": null
        }
      ],
      "colorCodes": null,
      "category": "ONE_DIMENSIONAL"
    }
  }
}
```

### Alternative Payload Format (JSON String)

```json
{
  "tool": "lead-report",
  "input": {
    "payload": "{\"rules\":[],\"groupBy\":[{\"name\":\"ownerId\",\"format\":null,\"primaryField\":null,\"property\":null}],\"dateRange\":{\"operator\":\"current_year\",\"field\":\"createdAt\",\"id\":\"createdAt\",\"type\":\"date\",\"value\":null,\"from\":\"00:00\",\"to\":\"23:59\",\"fieldInputType\":\"DATETIME_PICKER\",\"primaryField\":null,\"property\":null},\"metrics\":[{\"type\":\"COUNT\",\"field\":\"id\",\"header\":null}],\"colorCodes\":null,\"category\":\"ONE_DIMENSIONAL\"}"
  }
}
```

## Response Format

The lead-report tool returns the following response format:

```json
{
  "success": true,
  "tool": "lead-report",
  "result": {
    "reportId": "uuid-here",
    "title": "COUNT of id by ownerId",
    "generatedAt": "2024-01-15T10:30:00",
    "data": [
      {
        "groupValues": {"ownerId": "john.doe"},
        "metricValues": {"id": 25}
      },
      {
        "groupValues": {"ownerId": "jane.smith"},
        "metricValues": {"id": 18}
      }
    ],
    "summary": {
      "totalRows": 2,
      "aggregates": {"total_id": 43}
    },
    "metadata": {
      "totalRecordsProcessed": 100,
      "generatedAt": "2024-01-15T10:30:00",
      "category": "ONE_DIMENSIONAL"
    }
  }
}
```

## MCP Client Integration Examples

### Python MCP Client

```python
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def generate_lead_report():
    server_params = StdioServerParameters(
        command="java",
        args=["-jar", "mcp-server.jar"]
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize the session
            await session.initialize()
            
            # Generate report using structured payload
            payload = {
                "reportRequest": {
                    "rules": [],
                    "groupBy": [{"name": "ownerId"}],
                    "dateRange": {
                        "operator": "current_month",
                        "field": "createdAt",
                        "id": "createdAt",
                        "type": "date",
                        "from": "00:00",
                        "to": "23:59",
                        "fieldInputType": "DATETIME_PICKER"
                    },
                    "metrics": [{"type": "COUNT", "field": "id"}],
                    "category": "ONE_DIMENSIONAL"
                }
            }

            result = await session.call_tool("lead-report", payload)
            
            print(f"Report generated: {result}")

# Run the example
asyncio.run(generate_lead_report())
```

### JavaScript MCP Client

```javascript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function generateLeadReport() {
  const transport = new StdioClientTransport({
    command: 'java',
    args: ['-jar', 'mcp-server.jar']
  });

  const client = new Client({
    name: "lead-report-client",
    version: "1.0.0"
  }, {
    capabilities: {}
  });

  await client.connect(transport);

  // Generate report
  const result = await client.callTool({
    name: "lead-report",
    arguments: {
      reportRequest: {
        rules: [],
        groupBy: [{name: "ownerId"}],
        dateRange: {
          operator: "current_year",
          field: "createdAt",
          id: "createdAt",
          type: "date",
          from: "00:00",
          to: "23:59",
          fieldInputType: "DATETIME_PICKER"
        },
        metrics: [{type: "SUM", field: "value"}],
        category: "ONE_DIMENSIONAL"
      }
    }
  });

  console.log('Report result:', result);
}

generateLeadReport().catch(console.error);
```

## Error Handling

### Common Errors

1. **Missing Required Fields**
```json
{
  "success": false,
  "tool": "lead-report",
  "error": "Missing required field: reportRequest or payload"
}
```

## Best Practices

1. **Validate Payload Structure**: Ensure the JSON payload matches the expected schema
2. **Handle Errors Gracefully**: Always check the `success` field in responses
3. **Cache Results**: Reports can be expensive to generate, consider caching results
4. **Use Appropriate Metrics**: Choose COUNT, SUM, or AVERAGE based on your data type
5. **Optimize Date Ranges**: Use specific date operators for better performance

## Supported Metrics

- **COUNT**: Count of records in each group
- **SUM**: Sum of numeric field values in each group  
- **AVERAGE/AVG**: Average of numeric field values in each group

## Supported Date Ranges

- **current_year**: From January 1st to December 31st of current year
- **current_month**: From 1st to last day of current month
- **last_30_days**: From 30 days ago to today

## Available Fields

| Field Name | Type    | Description                    |
|------------|---------|--------------------------------|
| id         | Number  | Lead identifier (for counting) |
| ownerId    | String  | Lead owner identifier          |
| source     | String  | Lead source                    |
| status     | String  | Lead status                    |
| value      | Number  | Lead value (for sum/average)   |
| score      | Number  | Lead score (for sum/average)   |
| createdAt  | Date    | Lead creation date             |

## Payload Structure Reference

### Complete Payload Example
```json
{
  "rules": [
    {
      "field": "status",
      "operator": "equals",
      "value": "qualified",
      "condition": "AND"
    }
  ],
  "groupBy": [
    {
      "name": "ownerId",
      "format": null,
      "primaryField": null,
      "property": null
    }
  ],
  "dateRange": {
    "operator": "current_year",
    "field": "createdAt",
    "id": "createdAt",
    "type": "date",
    "value": null,
    "from": "00:00",
    "to": "23:59",
    "fieldInputType": "DATETIME_PICKER",
    "primaryField": null,
    "property": null
  },
  "metrics": [
    {
      "type": "COUNT",
      "field": "id",
      "header": null
    }
  ],
  "colorCodes": null,
  "category": "ONE_DIMENSIONAL"
}
```
