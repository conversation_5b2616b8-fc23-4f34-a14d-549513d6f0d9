package com.sell.mcp.service;

import com.sell.mcp.dto.leadreport.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for processing natural language prompts and converting them to structured lead report requests
 */
@Service
public class PromptProcessingService {
    
    private static final Logger logger = LoggerFactory.getLogger(PromptProcessingService.class);
    
    // Patterns for different types of queries
    private static final Pattern COUNT_PATTERN = Pattern.compile(
        "(?i)\\b(count|number)\\s+of\\s+(\\w+)\\s+by\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
    
    private static final Pattern SUM_PATTERN = Pattern.compile(
        "(?i)\\b(sum|total)\\s+of\\s+(\\w+)\\s+by\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
    
    private static final Pattern AVERAGE_PATTERN = Pattern.compile(
        "(?i)\\b(average|avg)\\s+of\\s+(\\w+)\\s+by\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
    
    private static final Pattern SHOW_BY_PATTERN = Pattern.compile(
        "(?i)\\bshow\\s+(\\w+)\\s+by\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
    
    private static final Pattern DATE_RANGE_PATTERN = Pattern.compile(
        "(?i)\\b(this year|current year|this month|current month|last 30 days|past month)", Pattern.CASE_INSENSITIVE);
    
    // Field mappings
    private static final Map<String, String> FIELD_MAPPINGS = Map.of(
        "lead", "id",
        "leads", "id",
        "owner", "ownerId",
        "owners", "ownerId",
        "source", "source",
        "sources", "source",
        "status", "status",
        "value", "value",
        "values", "value",
        "score", "score",
        "scores", "score"
    );
    
    /**
     * Process a natural language prompt and convert it to a LeadReportRequest
     */
    public LeadReportRequest processPrompt(String prompt) {
        logger.info("Processing prompt: {}", prompt);
        
        LeadReportRequest request = new LeadReportRequest();
        request.setRules(new ArrayList<>());
        request.setCategory("ONE_DIMENSIONAL");
        
        try {
            // Extract metric and groupBy information
            extractMetricAndGroupBy(prompt, request);
            
            // Extract date range information
            extractDateRange(prompt, request);
            
            // Set default values if not specified
            setDefaults(request);
            
            logger.info("Successfully processed prompt into request: {}", request);
            return request;
            
        } catch (Exception e) {
            logger.error("Error processing prompt: {}", prompt, e);
            throw new IllegalArgumentException("Failed to process prompt: " + e.getMessage(), e);
        }
    }
    
    /**
     * Extract metric type and groupBy field from the prompt
     */
    private void extractMetricAndGroupBy(String prompt, LeadReportRequest request) {
        List<Metric> metrics = new ArrayList<>();
        List<GroupByField> groupByFields = new ArrayList<>();
        
        // Try COUNT pattern
        Matcher countMatcher = COUNT_PATTERN.matcher(prompt);
        if (countMatcher.find()) {
            String entity = countMatcher.group(2);
            String groupByField = countMatcher.group(3);
            
            String metricField = mapField(entity);
            String groupField = mapField(groupByField);
            
            metrics.add(new Metric(Metric.MetricType.COUNT, metricField));
            groupByFields.add(new GroupByField(groupField));
            
            request.setMetrics(metrics);
            request.setGroupBy(groupByFields);
            return;
        }
        
        // Try SUM pattern
        Matcher sumMatcher = SUM_PATTERN.matcher(prompt);
        if (sumMatcher.find()) {
            String entity = sumMatcher.group(2);
            String groupByField = sumMatcher.group(3);
            
            String metricField = mapField(entity);
            String groupField = mapField(groupByField);
            
            metrics.add(new Metric(Metric.MetricType.SUM, metricField));
            groupByFields.add(new GroupByField(groupField));
            
            request.setMetrics(metrics);
            request.setGroupBy(groupByFields);
            return;
        }
        
        // Try AVERAGE pattern
        Matcher avgMatcher = AVERAGE_PATTERN.matcher(prompt);
        if (avgMatcher.find()) {
            String entity = avgMatcher.group(2);
            String groupByField = avgMatcher.group(3);
            
            String metricField = mapField(entity);
            String groupField = mapField(groupByField);
            
            metrics.add(new Metric(Metric.MetricType.AVERAGE, metricField));
            groupByFields.add(new GroupByField(groupField));
            
            request.setMetrics(metrics);
            request.setGroupBy(groupByFields);
            return;
        }
        
        // Try SHOW BY pattern (defaults to COUNT)
        Matcher showMatcher = SHOW_BY_PATTERN.matcher(prompt);
        if (showMatcher.find()) {
            String entity = showMatcher.group(1);
            String groupByField = showMatcher.group(2);
            
            String metricField = mapField(entity);
            String groupField = mapField(groupByField);
            
            metrics.add(new Metric(Metric.MetricType.COUNT, metricField));
            groupByFields.add(new GroupByField(groupField));
            
            request.setMetrics(metrics);
            request.setGroupBy(groupByFields);
            return;
        }
        
        // Default fallback
        logger.warn("Could not extract specific metric/groupBy from prompt, using defaults");
        metrics.add(new Metric(Metric.MetricType.COUNT, "id"));
        groupByFields.add(new GroupByField("ownerId"));
        
        request.setMetrics(metrics);
        request.setGroupBy(groupByFields);
    }
    
    /**
     * Extract date range information from the prompt
     */
    private void extractDateRange(String prompt, LeadReportRequest request) {
        Matcher dateMatcher = DATE_RANGE_PATTERN.matcher(prompt);
        
        DateRange dateRange = new DateRange();
        dateRange.setField("createdAt");
        dateRange.setId("createdAt");
        dateRange.setType("date");
        dateRange.setFrom("00:00");
        dateRange.setTo("23:59");
        dateRange.setFieldInputType("DATETIME_PICKER");
        
        if (dateMatcher.find()) {
            String dateText = dateMatcher.group(1).toLowerCase();
            
            switch (dateText) {
                case "this year":
                case "current year":
                    dateRange.setOperator("current_year");
                    break;
                case "this month":
                case "current month":
                    dateRange.setOperator("current_month");
                    break;
                case "last 30 days":
                case "past month":
                    dateRange.setOperator("last_30_days");
                    break;
                default:
                    dateRange.setOperator("current_year");
            }
        } else {
            // Default to current year
            dateRange.setOperator("current_year");
        }
        
        request.setDateRange(dateRange);
    }
    
    /**
     * Map natural language field names to database field names
     */
    private String mapField(String field) {
        String normalizedField = field.toLowerCase().trim();
        return FIELD_MAPPINGS.getOrDefault(normalizedField, normalizedField);
    }
    
    /**
     * Set default values for the request
     */
    private void setDefaults(LeadReportRequest request) {
        if (request.getRules() == null) {
            request.setRules(new ArrayList<>());
        }
        
        if (request.getCategory() == null) {
            request.setCategory("ONE_DIMENSIONAL");
        }
        
        if (request.getMetrics() == null || request.getMetrics().isEmpty()) {
            request.setMetrics(List.of(new Metric(Metric.MetricType.COUNT, "id")));
        }
        
        if (request.getGroupBy() == null || request.getGroupBy().isEmpty()) {
            request.setGroupBy(List.of(new GroupByField("ownerId")));
        }
        
        if (request.getDateRange() == null) {
            DateRange defaultDateRange = new DateRange("current_year", "createdAt");
            defaultDateRange.setFrom("00:00");
            defaultDateRange.setTo("23:59");
            defaultDateRange.setFieldInputType("DATETIME_PICKER");
            request.setDateRange(defaultDateRange);
        }
    }
    
    /**
     * Get example prompts that this service can process
     */
    public List<String> getExamplePrompts() {
        return List.of(
            "count of leads by owner",
            "show leads by owner",
            "sum of value by owner",
            "average score by source",
            "count of leads by status this year",
            "total value by owner this month",
            "show leads by source last 30 days"
        );
    }
    
    /**
     * Get supported field mappings
     */
    public Map<String, String> getSupportedFields() {
        return new HashMap<>(FIELD_MAPPINGS);
    }
}
