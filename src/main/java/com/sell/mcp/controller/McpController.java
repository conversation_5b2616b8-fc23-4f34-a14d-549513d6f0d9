package com.sell.mcp.controller;

import com.sell.mcp.config.McpServerConfig;
import com.sell.mcp.entity.McpTool;
import com.sell.mcp.service.McpToolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Main MCP (Model Context Protocol) Controller
 * Handles MCP protocol endpoints and tool management
 */
@RestController
@RequestMapping("/mcp")
@CrossOrigin(origins = "*")
public class McpController {
    
    private static final Logger logger = LoggerFactory.getLogger(McpController.class);
    
    private final McpServerConfig config;
    private final McpToolService toolService;
    
    @Autowired
    public McpController(McpServerConfig config, McpToolService toolService) {
        this.config = config;
        this.toolService = toolService;
    }
    
    /**
     * MCP Server Information endpoint
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getServerInfo() {
        logger.debug("Received request for server info");
        
        Map<String, Object> serverInfo = Map.of(
            "name", config.getName(),
            "version", config.getVersion(),
            "description", config.getDescription(),
            "capabilities", config.getCapabilities(),
            "timestamp", LocalDateTime.now().toString()
        );
        
        return ResponseEntity.ok(serverInfo);
    }
    
    /**
     * List all available tools
     */
    @GetMapping("/tools")
    public ResponseEntity<List<McpTool>> listTools() {
        logger.debug("Received request to list all tools");
        List<McpTool> tools = toolService.getAllTools();
        return ResponseEntity.ok(tools);
    }
    
    /**
     * Get specific tool by name
     */
    @GetMapping("/tools/{name}")
    public ResponseEntity<McpTool> getTool(@PathVariable String name) {
        logger.debug("Received request for tool: {}", name);
        
        Optional<McpTool> tool = toolService.getToolByName(name);
        return tool.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * Execute a tool
     */
    @PostMapping("/tools/{name}/execute")
    public ResponseEntity<Map<String, Object>> executeTool(
            @PathVariable String name,
            @RequestBody Map<String, Object> input) {
        
        logger.info("Received request to execute tool: {} with input: {}", name, input);
        
        try {
            Map<String, Object> result = toolService.executeTool(name, input);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            logger.error("Tool execution failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during tool execution", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * Create or update a tool
     */
    @PostMapping("/tools")
    public ResponseEntity<McpTool> createOrUpdateTool(@RequestBody Map<String, Object> toolData) {
        logger.info("Received request to create/update tool: {}", toolData.get("name"));
        
        try {
            String name = (String) toolData.get("name");
            String description = (String) toolData.get("description");
            @SuppressWarnings("unchecked")
            Map<String, Object> inputSchema = (Map<String, Object>) toolData.get("inputSchema");
            
            if (name == null || name.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            McpTool tool = toolService.saveOrUpdateTool(name, description, inputSchema);
            return ResponseEntity.ok(tool);
            
        } catch (Exception e) {
            logger.error("Error creating/updating tool", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Delete a tool
     */
    @DeleteMapping("/tools/{name}")
    public ResponseEntity<Void> deleteTool(@PathVariable String name) {
        logger.info("Received request to delete tool: {}", name);
        
        boolean deleted = toolService.deleteTool(name);
        return deleted ? ResponseEntity.noContent().build() 
                      : ResponseEntity.notFound().build();
    }
    
    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = Map.of(
            "status", "UP",
            "timestamp", LocalDateTime.now().toString(),
            "server", config.getName()
        );
        
        return ResponseEntity.ok(health);
    }
}
