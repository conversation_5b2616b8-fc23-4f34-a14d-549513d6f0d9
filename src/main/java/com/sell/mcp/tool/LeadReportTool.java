package com.sell.mcp.tool;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.mcp.dto.leadreport.LeadReportRequest;
import com.sell.mcp.dto.leadreport.LeadReportResponse;
import com.sell.mcp.service.LeadReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * MCP Tool for generating lead reports
 * Handles the lead-report tool execution with complex payload processing
 */
@Component
public class LeadReportTool {
    
    private static final Logger logger = LoggerFactory.getLogger(LeadReportTool.class);
    private static final String TOOL_NAME = "lead-report";
    
    private final LeadReportService leadReportService;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public LeadReportTool(LeadReportService leadReportService, ObjectMapper objectMapper) {
        this.leadReportService = leadReportService;
        this.objectMapper = objectMapper;
    }
    
    /**
     * Execute the lead-report tool with the given input
     */
    public Map<String, Object> execute(Map<String, Object> input) {
        logger.info("Executing lead-report tool with input: {}", input);
        
        try {
            // Extract the report request from input
            LeadReportRequest request = parseLeadReportRequest(input);
            
            // Generate the report
            LeadReportResponse response = leadReportService.generateLeadReport(request);
            
            // Convert response to Map for MCP protocol
            Map<String, Object> result = objectMapper.convertValue(response, Map.class);
            
            logger.info("Successfully executed lead-report tool, generated report with ID: {}", 
                       response.getReportId());
            
            return Map.of(
                "success", true,
                "tool", TOOL_NAME,
                "result", result
            );
            
        } catch (Exception e) {
            logger.error("Error executing lead-report tool", e);
            return Map.of(
                "success", false,
                "tool", TOOL_NAME,
                "error", e.getMessage(),
                "details", e.getClass().getSimpleName()
            );
        }
    }
    
    /**
     * Parse the input map into a LeadReportRequest object
     */
    private LeadReportRequest parseLeadReportRequest(Map<String, Object> input) {
        try {
            // Check if input contains the report request directly
            if (input.containsKey("reportRequest")) {
                Object reportRequestObj = input.get("reportRequest");
                return objectMapper.convertValue(reportRequestObj, LeadReportRequest.class);
            }
            
            // Check if input contains individual fields
            if (input.containsKey("rules") || input.containsKey("groupBy") || 
                input.containsKey("dateRange") || input.containsKey("metrics")) {
                return objectMapper.convertValue(input, LeadReportRequest.class);
            }
            
            // If input contains a JSON string
            if (input.containsKey("payload")) {
                String payload = input.get("payload").toString();
                return objectMapper.readValue(payload, LeadReportRequest.class);
            }
            
            throw new IllegalArgumentException("Invalid input format. Expected reportRequest, payload, or individual fields (rules, groupBy, dateRange, metrics)");
            
        } catch (Exception e) {
            logger.error("Error parsing lead report request", e);
            throw new IllegalArgumentException("Failed to parse lead report request: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get the tool name
     */
    public String getToolName() {
        return TOOL_NAME;
    }
    
    /**
     * Get the tool description
     */
    public String getDescription() {
        return "Generate lead reports with grouping, filtering, and various metrics (COUNT, SUM, AVERAGE). " +
               "Supports complex queries with date ranges, rules, and multiple grouping fields.";
    }
    
    /**
     * Get the input schema for this tool
     */
    public Map<String, Object> getInputSchema() {
        return Map.of(
            "type", "object",
            "properties", Map.of(
                "reportRequest", Map.of(
                    "type", "object",
                    "description", "Complete lead report request object",
                    "properties", Map.of(
                        "rules", Map.of(
                            "type", "array",
                            "description", "Filtering rules to apply",
                            "items", Map.of(
                                "type", "object",
                                "properties", Map.of(
                                    "field", Map.of("type", "string"),
                                    "operator", Map.of("type", "string"),
                                    "value", Map.of("type", "string"),
                                    "condition", Map.of("type", "string")
                                )
                            )
                        ),
                        "groupBy", Map.of(
                            "type", "array",
                            "description", "Fields to group by",
                            "items", Map.of(
                                "type", "object",
                                "properties", Map.of(
                                    "name", Map.of("type", "string"),
                                    "format", Map.of("type", "string"),
                                    "primaryField", Map.of("type", "string"),
                                    "property", Map.of("type", "string")
                                )
                            )
                        ),
                        "dateRange", Map.of(
                            "type", "object",
                            "description", "Date range filter",
                            "properties", Map.of(
                                "operator", Map.of("type", "string", "enum", new String[]{"current_year", "current_month", "last_30_days"}),
                                "field", Map.of("type", "string"),
                                "id", Map.of("type", "string"),
                                "type", Map.of("type", "string"),
                                "value", Map.of("type", "string"),
                                "from", Map.of("type", "string"),
                                "to", Map.of("type", "string"),
                                "fieldInputType", Map.of("type", "string"),
                                "primaryField", Map.of("type", "string"),
                                "property", Map.of("type", "string")
                            )
                        ),
                        "metrics", Map.of(
                            "type", "array",
                            "description", "Metrics to calculate",
                            "items", Map.of(
                                "type", "object",
                                "properties", Map.of(
                                    "type", Map.of("type", "string", "enum", new String[]{"COUNT", "SUM", "AVERAGE"}),
                                    "field", Map.of("type", "string"),
                                    "header", Map.of("type", "string")
                                )
                            )
                        ),
                        "category", Map.of("type", "string")
                    )
                ),
                "payload", Map.of(
                    "type", "string",
                    "description", "JSON string containing the complete report request"
                )
            ),
            "oneOf", new Object[]{
                Map.of("required", new String[]{"reportRequest"}),
                Map.of("required", new String[]{"payload"})
            }
        );
    }
}
