server:
  port: 8080
  servlet:
    context-path: /api
  # Increase header size limits for large Bearer tokens and gateway headers
  max-http-header-size: 64KB
  max-http-request-header-size: 64KB
  tomcat:
    max-http-header-size: 65536
    max-http-request-header-size: 65536
    connection-timeout: 20000
    max-connections: 8192

spring:
  application:
    name: mcp-server
  
  # Database Configuration
  datasource:
    url: **************************************
    username: mcpuser
    password: mcppass
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false
  
  # Flyway Configuration
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    clean-disabled: true
  
  # Spring AI Configuration
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:your-openai-api-key}
      chat:
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
          max-tokens: 1000
    
    ollama:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        options:
          model: llama2
          temperature: 0.7

# Logging Configuration
logging:
  level:
    com.sell.mcp: DEBUG
    org.springframework.ai: DEBUG
    org.flywaydb: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Management and Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# MCP Server Configuration
mcp:
  server:
    name: "Spring AI MCP Server"
    version: "1.0.0"
    description: "Model Context Protocol server built with Spring AI"
    capabilities:
      - tools
      - resources
      - prompts
    max-request-size: 10MB
    timeout: 30s
  security:
    validate-token: true
    trusted-gateway-header: "X-Gateway-User"
