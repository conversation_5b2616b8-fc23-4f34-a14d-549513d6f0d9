# Spring AI MCP Server

A Model Context Protocol (MCP) server built with Spring AI, Spring Boot, PostgreSQL, and Flyway.

## Features

- **Spring AI Integration**: Built on Spring AI framework for AI/ML capabilities
- **MCP Protocol Support**: Implements Model Context Protocol for tool execution
- **PostgreSQL Database**: Persistent storage with Flyway migrations
- **RESTful API**: HTTP endpoints for MCP operations
- **Tool Management**: Create, execute, and manage AI tools
- **Session Management**: Track client sessions and tool executions

## Prerequisites

- Java 17 or higher
- Maven 3.6+
- Docker and Docker Compose (for PostgreSQL)

## Quick Start

### 1. Start PostgreSQL Database

```bash
# Start PostgreSQL using Docker Compose
docker-compose up -d postgres

# Verify database is running
docker-compose ps
```

### 2. Build and Run the Application

```bash
# Build the application
mvn clean compile

# Run database migrations
mvn flyway:migrate

# Start the application
mvn spring-boot:run
```

### 3. Verify Installation

```bash
# Check server health
curl http://localhost:8080/api/mcp/health

# Get server information
curl http://localhost:8080/api/mcp/info

# List available tools
curl http://localhost:8080/api/mcp/tools
```

## API Endpoints

### Server Information
- `GET /api/mcp/info` - Get server information and capabilities
- `GET /api/mcp/health` - Health check endpoint

### Tool Management
- `GET /api/mcp/tools` - List all available tools
- `GET /api/mcp/tools/{name}` - Get specific tool details
- `POST /api/mcp/tools` - Create or update a tool
- `DELETE /api/mcp/tools/{name}` - Delete a tool
- `POST /api/mcp/tools/{name}/execute` - Execute a tool

## Configuration

### Application Properties

Key configuration options in `src/main/resources/application.yml`:

```yaml
spring:
  datasource:
    url: **************************************
    username: mcpuser
    password: mcppass
  
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:your-openai-api-key}
    ollama:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}

mcp:
  server:
    name: "Spring AI MCP Server"
    capabilities: ["tools", "resources", "prompts"]
```

### Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key (optional)
- `OLLAMA_BASE_URL`: Ollama server URL (optional, defaults to localhost:11434)

## Database Management

### Using Flyway

```bash
# Run migrations
mvn flyway:migrate

# Check migration status
mvn flyway:info

# Clean database (development only)
mvn flyway:clean
```

### Using PgAdmin (Optional)

If you started the full Docker Compose stack:

1. Open http://localhost:8081
2. Login with: <EMAIL> / admin123
3. Add server: localhost:5432, mcpuser/mcppass

## Development

### Project Structure

```
src/
├── main/
│   ├── java/com/sell/mcp/
│   │   ├── config/          # Configuration classes
│   │   ├── controller/      # REST controllers
│   │   ├── entity/          # JPA entities
│   │   ├── repository/      # Data repositories
│   │   ├── service/         # Business logic
│   │   └── McpServerApplication.java
│   └── resources/
│       ├── db/migration/    # Flyway migrations
│       └── application.yml  # Configuration
└── test/                    # Test classes
```

### Adding New Tools

1. Create tool entry in database or via API
2. Implement tool execution logic in `McpToolService`
3. Add any required AI model integrations

### Testing

```bash
# Run all tests
mvn test

# Run with test containers (requires Docker)
mvn test -Dspring.profiles.active=test
```

## Docker Support

### Full Stack with Docker Compose

```bash
# Start all services (PostgreSQL + PgAdmin)
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Database Only

```bash
# Start only PostgreSQL
docker-compose up -d postgres
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
